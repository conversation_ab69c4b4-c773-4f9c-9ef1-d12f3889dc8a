Metadata-Version: 2.1
Name: my-fake-useragent
Version: 0.2.1
Summary: create a fake useragent
Home-page: https://github.com/a358003542/my-fake-useragent
Author: wanze
Author-email: <EMAIL>
Maintainer: wanze
Maintainer-email: <EMAIL>
License: MIT
Keywords: useragent,python
Classifier: Development Status :: 4 - Beta
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX :: Linux
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Description-Content-Type: text/markdown
License-File: LICENSE

# my-fake-useragent
generate your random fake useragent.


## description:
create your fake useragent

## install
```
python setup.py install
```

or
```text
pip install my_fake_useragent
```
    
## usage

```python
from my_fake_useragent import UserAgent

ua = UserAgent(family='chrome')

res = ua.random()

```

### family参数可用：
可以设置列表还匹配多种情况

- chrome
- firefox
- edge
- ie
- opera
- safari

### os_family参数可用
可以设置列表还匹配多种情况

- android
- windows
- linux
- mac
- ios
- chrome os

### phone参数可用

- None 不过滤
- True 要求是移动端
- False 要求不是移动端

移动端的判据如下：
如果device family检测到：

```
[
    'BlackBerry',
    'BlackBerry 9700',
    'BlackBerry 9800',
    'Generic Feature Phone',
    'Generic Smartphone',
    'Generic Tablet',
    'HTC Desire',
    'HTC DesireHD A9191',
    'HTC DesireS S510e',
    'HTC DesireZ A7272',
    'HTC HD2_T8585',
    'HTC IncredibleS S710e',
    'HTC Legend',
    'HTC P715a',
    'HTC Pyramid',
    'HTC Sensation',
    'HTC Vision',
    'LG-L160L',
    'LG-LU3000',
    'LG-P505R',
    'Nintendo Wii',
    'Nokia 2730c-1',
    'SprintPPC-6700',
    'SprintPPC-6700)',
    'SprintPPC-i830',
    'SprintSCH-i320',
    'SprintSCH-i830',
    'SprintSPH-ip320',
    'SprintSPH-ip830w',
    'T-Mobile myTouch 3G Slide',
    'iPad',
    'iPhone',
    'iPod']
```

则认为是移动端，同时还补充 认为 android操作系统和ios操作系统也是移动端。


一个符合过滤例子如下：

要求 linux操作系统的的chrome 浏览器:

```
from my_fake_useragent import UserAgent
ua = UserAgent(family='chrome', os_family='linux')
```

## CHANGELOG
### 0.2.1
更新了最新的数据，看了一下，就Chrome firefox等增加了一些新的版本。项目很久没接触了，其他的没做修改。

浏览器现在变动得也比较少了，本项目就生成一些随机的useragent基本上够用了，打算封存了。

### 0.2.0
make the parsed_data.json file to a .py file which will make this package more compatible with the pyinstaller package.

### 0.1.6
nothing change on the source code, but add the pypi wheel to speed up installation.

### 0.1.1
this module does not need any third module at all.

### 0.1.0
init


