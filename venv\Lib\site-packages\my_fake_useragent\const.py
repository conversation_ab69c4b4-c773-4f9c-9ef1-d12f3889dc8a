#!/usr/bin/env python
# -*-coding:utf-8-*-


SUPPORT_TYPES = [
    'Chrome',
    'Edge',
    'Firefox',
    'Opera',
    'Internet Explorer',
    'Safari',
    'Tencent Traveler',
    'Android Webkit Browser',
    'Opera Mini',
    'Opera Mobile',
]

FAMILY_MAP = {
    'chrome': ['chrome', 'Chrome', 'Chrome Frame', 'Chromium'],
    'edge': ['Edge', 'edge'],
    'firefox': ['Firefox', 'firefox', 'Firefox Alpha', 'Firefox Beta'],
    'ie': ['IE', 'ie'],
    'opera': ['opera', 'Opera'],
    'safari': ['Safari', 'safari'],
}

OS_FAMILY_MAP = {
    'android': ['android', 'Android'],
    'windows': ['Windows',
                'Windows 2000',
                'Windows 3.1',
                'Windows 95',
                'Windows 98',
                'Windows CE',
                'Windows ME',
                'Windows Mobile',
                'Windows NT 4.0',
                'Windows XP', ],
    'linux': [
        'Arch Linux',
        'CentOS',
        'Chrome OS',
        'Debian',
        'Fedora',
        'FreeBSD',
        'Gentoo',
        'Kubuntu',
        'Linux',
        'Linux Mint',
        'NetBSD',
        'OpenBSD',
        'Red Hat',
        'SUSE',
        'Solaris',
        'Symbian OS',
        'Ubuntu', ],
    'mac': ['Mac OS',
            'Mac OS X'],
    'ios': ['iOS'],
    'chrome os': ['Chrome OS']
}

PHONE_DEVICE_FAMILY_LIST = [
    'BlackBerry',
    'BlackBerry 9700',
    'BlackBerry 9800',
    'Generic Feature Phone',
    'Generic Smartphone',
    'Generic Tablet',
    'HTC Desire',
    'HTC DesireHD A9191',
    'HTC DesireS S510e',
    'HTC DesireZ A7272',
    'HTC HD2_T8585',
    'HTC IncredibleS S710e',
    'HTC Legend',
    'HTC P715a',
    'HTC Pyramid',
    'HTC Sensation',
    'HTC Vision',
    'LG-L160L',
    'LG-LU3000',
    'LG-P505R',
    'Nintendo Wii',
    'Nokia 2730c-1',
    'SprintPPC-6700',
    'SprintPPC-6700)',
    'SprintPPC-i830',
    'SprintSCH-i320',
    'SprintSCH-i830',
    'SprintSPH-ip320',
    'SprintSPH-ip830w',
    'T-Mobile myTouch 3G Slide',
    'iPad',
    'iPhone',
    'iPod']
